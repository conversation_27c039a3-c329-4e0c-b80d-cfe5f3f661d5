import React from 'react';
import { Button } from '@/components/ui/button';
import Bar<PERSON>hart from '@/components/dashboard/BarChart';
import LoadingUI from '@/components/ui/loading';
import { analyticsApi } from '@/services/api';

interface CandidateSourcesChartProps {
  loading: boolean;
  data?: {
    sources?: Array<{
      name: string;
      applications: number;
      candidates: number;
      percentage: number;
      conversionRate: number;
    }>;
    sourceEffectiveness?: Array<{
      source: string;
      applications: number;
      candidates?: number;
      percentage?: number;
      conversionRate?: number;
    }>;
  } | null;
}

const CandidateSourcesChart: React.FC<CandidateSourcesChartProps> = ({ loading, data }) => {

  // Prepare chart data with fallback
  const getChartData = () => {
    // Check for sourceEffectiveness first (actual API response)
    if (data?.sourceEffectiveness && data.sourceEffectiveness.length > 0) {
      return data.sourceEffectiveness
        .map(item => ({
          name: item.source,
          applications: item.applications,
          value: item.applications
        }))
        .sort((a, b) => (b.applications || 0) - (a.applications || 0));
    }

    // Check for sources (alternative API response format)
    if (data?.sources && data.sources.length > 0) {
      return data.sources
        .map(item => ({
          ...item,
          name: item.name,
          value: item.applications
        }))
        .sort((a, b) => (b.applications || 0) - (a.applications || 0));
    }

    // Fallback data
    return [
      { name: 'LinkedIn', applications: 25, value: 25 },
      { name: 'Company Website', applications: 10, value: 10 },
      { name: 'Indeed', applications: 6, value: 6 },
      { name: 'Glassdoor', applications: 6, value: 6 },
      { name: 'Employee Referral', applications: 3, value: 3 }
    ].sort((a, b) => b.applications - a.applications);
  };

  const chartData = getChartData();
  const dynamicHeight = Math.max(160, Math.min(220, 40 + (chartData.length * 28)));

  if (loading) {
    return (
      <div className="h-[220px] flex items-center justify-center">
        <LoadingUI message="Loading source data..." />
      </div>
    );
  }

  if (chartData.length === 0) {
    return (
      <div className="h-[250px] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <div className="text-slate-500 text-base mb-4">No source data available</div>
          <p className="text-xs text-slate-600 mb-4">
            Source tracking helps identify the most effective recruitment channels
          </p>
          <Button
            onClick={async () => {
              try {
                await analyticsApi.initializeCandidateSources();
                window.location.reload();
              } catch (error) {
                console.error('Failed to initialize sources:', error);
              }
            }}
            className="bg-blue-600 hover:bg-blue-700 mx-auto"
          >
            Initialize Source Data
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full" style={{ height: `${dynamicHeight}px` }}>
      <BarChart
        title=""
        description=""
        data={chartData}
        bars={[{ dataKey: "applications", fill: "#2563EB", name: "Applications" }]}
        vertical={true}
        height={dynamicHeight}
        valueFormatter={(value) => `${value} applications`}
        showTooltip={true}
        showLegend={false}
        className="w-full h-full"
      />
    </div>
  );
};

export default CandidateSourcesChart;
